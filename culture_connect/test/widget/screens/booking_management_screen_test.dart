import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/models/booking_model.dart' as models;
import 'package:culture_connect/screens/booking_management_screen.dart';
import 'package:culture_connect/services/booking_service.dart';

// Generate mocks for dependencies
@GenerateMocks([BookingService])
import 'booking_management_screen_test.mocks.dart';

void main() {
  late MockBookingService mockBookingService;

  setUp(() {
    mockBookingService = MockBookingService();

    // Setup mock responses with empty lists for simplicity
    when(mockBookingService.getUserBookings())
        .thenAnswer((_) async => <models.BookingModel>[]);

    when(mockBookingService.getUpcomingBookings())
        .thenAnswer((_) async => <models.BookingModel>[]);

    when(mockBookingService.cancelBooking(any)).thenAnswer((_) async {});

    when(mockBookingService.requestRefund(any, any))
        .thenAnswer((_) async => true);

    when(mockBookingService.addBookingToCalendar(any))
        .thenAnswer((_) async => true);
  });

  Widget createWidgetUnderTest() {
    return ProviderScope(
      overrides: [
        // Override the booking service provider to use our mock
        bookingServiceProvider.overrideWithValue(mockBookingService),
      ],
      child: const MaterialApp(
        home: BookingManagementScreen(),
      ),
    );
  }

  group('BookingManagementScreen Widget Tests', () {
    testWidgets('should render without errors', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(BookingManagementScreen), findsOneWidget);
    });

    testWidgets('should show empty state when no bookings',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - Should render without errors
      expect(find.byType(BookingManagementScreen), findsOneWidget);
    });
  });
}
